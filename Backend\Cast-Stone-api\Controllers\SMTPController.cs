﻿using Microsoft.AspNetCore.Mvc;
using System.Net;
using System.Net.Mail;

[ApiController]
[Route("api/[controller]")]
public class EmailController : ControllerBase
{
    private readonly IConfiguration _config;

    public EmailController(IConfiguration config)
    {
        _config = config;
    }

    [HttpPost("send")]
    public IActionResult SendEmail([FromBody] EmailRequest request)
    {
        var smtpSettings = _config.GetSection("SmtpSettings");

        using var client = new SmtpClient(smtpSettings["Host"], int.Parse(smtpSettings["Port"]))
        {
            Credentials = new NetworkCredential(smtpSettings["Username"], smtpSettings["Password"]),
            EnableSsl = bool.Parse(smtpSettings["EnableSsl"]),
        };

        var message = new MailMessage
        {
            From = new MailAddress(smtpSettings["FromEmail"], smtpSettings["FromName"]),
            Subject = request.Subject,
            Body = request.Message,
            IsBodyHtml = true
        };

        message.To.Add(smtpSettings["AdminEmail"]);

        client.Send(message);

        return Ok(new { status = "sent" });
    }
}

public class EmailRequest
{
    public string Subject { get; set; }
    public string Message { get; set; }
}
