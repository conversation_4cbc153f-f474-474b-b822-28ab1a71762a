{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "your-database-connection-string"}, "Cloudinary": {"CloudName": "your-cloudinary-cloud-name", "ApiKey": "your-cloudinary-api-key", "ApiSecret": "your-cloudinary-api-secret"}, "SmtpSettings": {"Host": "smtp-relay.brevo.com", "Port": 587, "Username": "<EMAIL>", "Password": "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", "EnableSsl": true, "FromEmail": "<EMAIL>", "FromName": "Cast Stone", "AdminEmail": "<EMAIL>"}, "Stripe": {"SecretKey": "your-stripe-secret-key", "PublishableKey": "your-stripe-publishable-key"}, "PayPal": {"ClientId": "your-paypal-client-id", "Secret": "your-paypal-secret", "Environment": "sandbox"}, "ApplePay": {"MerchantId": "merchant.com.example", "DomainVerificationPath": ".well-known/apple-developer-merchantid-domain-association"}}