/* Product Image Gallery - Sharp Rectangular Design */
.imageGallery {
  width: 100%;
}

/* Main Image Container */
.mainImageContainer {
  position: relative;
  background: #f8f8f8;
  border: 2px solid #ddd;
  border-radius: 0; /* Sharp corners */
  overflow: hidden;
  aspect-ratio: 1;
  margin-bottom: 1rem;
}

.mainImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: zoom-in;
  transition: transform 0.3s ease;
}

.mainImage.zoomed {
  cursor: zoom-out;
}

/* Navigation Buttons */
.navButton {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  width: 50px;
  height: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0; /* Sharp corners */
}

.navButton:hover {
  background: rgba(0, 0, 0, 0.9);
}

.prevButton {
  left: 10px;
}

.nextButton {
  right: 10px;
}

/* Image Counter */
.imageCounter {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  font-weight: 600;
  border-radius: 0; /* Sharp corners */
}

/* Zoom Indicator */
.zoomIndicator {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(255, 255, 255, 0.9);
  color: #4a3728;
  padding: 0.5rem;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 0; /* Sharp corners */
}

/* Thumbnail Container */
/* .thumbnailContainer {
  margin-top: 1rem;
}

.thumbnailGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 0.5rem;
  max-height: 200px;
  overflow-y: auto;
}

.thumbnail {
  background: none;
  border: 2px solid #ddd;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  transition: all 0.3s ease;
  aspect-ratio: 1;
  overflow: hidden;
}

.thumbnailImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
} */

.thumbnailContainer {
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
  padding: 8px 0;
}

.thumbnailGrid {
  display: flex;
  gap: 8px;
}

.thumbnail {
  display: inline-block;
  border: none;
  background: none;
  padding: 0;
  cursor: pointer;
  outline: none;
}

.thumbnailImage {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border: 2px solid transparent;
  border-radius: 4px;
  transition: border 0.3s;
}

.activeThumbnail .thumbnailImage {
  border-color: #0070f3; /* Highlight active */
}

.thumbnail:hover {
  border-color: #4a3728;
}

.thumbnail.activeThumbnail {
  border-color: #4a3728;
  border-width: 3px;
}



/* Zoom Overlay */
.zoomOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: zoom-out;
}

.zoomedImageContainer {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
}

.zoomedImage {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.closeZoomButton {
  position: absolute;
  top: -50px;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
  color: #4a3728;
  border: none;
  width: 40px;
  height: 40px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0; /* Sharp corners */
  transition: background-color 0.3s ease;
}

.closeZoomButton:hover {
  background: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .navButton {
    width: 40px;
    height: 40px;
  }
  
  .prevButton {
    left: 5px;
  }
  
  .nextButton {
    right: 5px;
  }
  
  .imageCounter {
    bottom: 10px;
    font-size: 0.8rem;
    padding: 0.25rem 0.75rem;
  }
  
  .zoomIndicator {
    top: 10px;
    right: 10px;
    padding: 0.25rem;
    font-size: 0.7rem;
  }
  
  .thumbnailGrid {
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
    gap: 0.25rem;
  }
  
  .zoomedImageContainer {
    max-width: 95vw;
    max-height: 95vh;
  }
  
  .closeZoomButton {
    top: -40px;
    width: 35px;
    height: 35px;
  }
}

@media (max-width: 480px) {
  .mainImageContainer {
    margin-bottom: 0.5rem;
  }
  
  .navButton {
    width: 35px;
    height: 35px;
  }
  
  .thumbnailGrid {
    grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
  }
}
